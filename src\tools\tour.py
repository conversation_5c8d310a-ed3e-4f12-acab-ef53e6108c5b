import datetime
import logging
from enum import Enum

from fastapi import APIRouter, HTTPException
from fastmcp import FastMCP
from starlette import status

from src.models.tour import ScheduleTourRequest, ScheduleTourResponse

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

router = APIRouter()


class TourStatus(str, Enum):
    success = "success"
    error = "error"
    conflict = "conflict"
    unavailable = "unavailable"


class InvalidTourDateError(ValueError):
    def __init__(self, value: str):
        super().__init__(f"Tour date '{value}' is invalid. Must be a future date and time.")


class InvalidPropertyIdError(ValueError):
    def __init__(self, value: int | None):
        super().__init__(f"Property ID '{value}' is invalid. Must be a positive integer.")


class InvalidRenterIdError(ValueError):
    def __init__(self, value: int | None):
        super().__init__(f"Renter ID '{value}' is invalid. Must be a positive integer.")


def validate_property_id(value: int | None) -> int:
    if value is None or value <= 0:
        raise InvalidPropertyIdError(value)
    return value


def validate_renter_id(value: int | None) -> int:
    if value is None or value <= 0:
        raise InvalidRenterIdError(value)
    return value


def validate_tour_date(value: datetime.datetime) -> datetime.datetime:
    if value <= datetime.datetime.now():
        raise InvalidTourDateError(value.isoformat())
    return value


def schedule_property_tour(
    property_id: int,
    renter_id: int,
    tour_date: datetime.datetime,
    first_name: str,
    last_name: str,
    preference_bedrooms: int | None = None,
    preference_move_date: str | None = None,
) -> ScheduleTourResponse:
    """
    Schedule a property tour for a prospect.

    Args:
        property_id: The ID of the property to tour
        renter_id: The ID of the prospect renter
        tour_date: The requested tour date and time
        first_name: Prospect's first name
        last_name: Prospect's last name
        preference_bedrooms: Optional bedroom preference (0-4)
        preference_move_date: Optional preferred move-in date (YYYY-MM-DD)

    Returns:
        ScheduleTourResponse with status, appointment_id, and message

    Raises:
        HTTPException: If validation fails or tour cannot be scheduled
    """
    try:
        validated_property_id = validate_property_id(property_id)
        validated_renter_id = validate_renter_id(renter_id)
        validated_tour_date = validate_tour_date(tour_date)
    except (InvalidPropertyIdError, InvalidRenterIdError, InvalidTourDateError) as e:
        raise HTTPException(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)) from e

    logger.info(
        "Scheduling tour for property %s, renter %s, date %s, name %s %s, bedrooms %s, move date %s",
        validated_property_id,
        validated_renter_id,
        validated_tour_date,
        first_name,
        last_name,
        preference_bedrooms,
        preference_move_date,
    )

    # Generate a deterministic appointment ID based on property and date
    composite_key = f"{validated_property_id}:{validated_renter_id}:{validated_tour_date.isoformat()}"
    appointment_id = abs(hash(composite_key)) % 10000000

    # Build message with optional preferences
    message_parts = [
        f"Tour scheduled successfully for {first_name} {last_name} at property {validated_property_id} on {validated_tour_date.strftime('%Y-%m-%d %H:%M')}."
    ]

    if preference_bedrooms is not None:
        message_parts.append(f"Bedroom preference: {preference_bedrooms}")

    if preference_move_date:
        message_parts.append(f"Preferred move-in date: {preference_move_date}")

    return ScheduleTourResponse(
        status=TourStatus.success,
        appointment_id=str(appointment_id),
        message=" ".join(message_parts),
    )


def register_tour_tools(mcp: FastMCP) -> None:
    @mcp.tool(
        name="schedule_property_tour",
        description="Schedule a property tour for a prospect renter.",
    )
    def schedule_tour_tool(
        property_id: int,
        renter_id: int,
        tour_date: datetime.datetime,
        first_name: str,
        last_name: str,
        preference_bedrooms: int | None = None,
        preference_move_date: str | None = None,
    ) -> ScheduleTourResponse:
        return schedule_property_tour(
            property_id, renter_id, tour_date, first_name, last_name, preference_bedrooms, preference_move_date
        )


@router.post("/schedule", response_model=ScheduleTourResponse)
async def schedule_tour(request: ScheduleTourRequest) -> ScheduleTourResponse:
    return schedule_property_tour(
        request.property_id,
        request.renter_id,
        request.tour_date,
        request.first_name,
        request.last_name,
        request.preference_bedrooms,
        request.preference_move_date,
    )
