from datetime import datetime, timedelta
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient
from starlette import status

from src.tools.tour import router

app = FastAPI()
app.include_router(router)

client = TestClient(app)


def test_schedule_tour_valid_request():
    """Test scheduling a tour with valid data."""
    future_date = datetime.now() + timedelta(days=1)

    response = client.post(
        "/schedule",
        json={
            "property_id": 123,
            "renter_id": 456,
            "tour_date": future_date.isoformat(),
            "first_name": "<PERSON>",
            "last_name": "<PERSON><PERSON>",
            "preference_bedrooms": 2,
            "preference_move_date": "2025-08-01",
        },
    )

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["status"] == "success"
    assert "appointment_id" in data
    assert "John Doe" in data["message"]
    assert "property 123" in data["message"]
    assert "Bedroom preference: 2" in data["message"]
    assert "Preferred move-in date: 2025-08-01" in data["message"]


def test_schedule_tour_minimal_request():
    """Test scheduling a tour with minimal required data."""
    future_date = datetime.now() + timedelta(days=1)

    response = client.post(
        "/schedule",
        json={
            "property_id": 123,
            "renter_id": 456,
            "tour_date": future_date.isoformat(),
            "first_name": "Jane",
            "last_name": "Smith",
        },
    )

    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["status"] == "success"
    assert "appointment_id" in data
    assert "Jane Smith" in data["message"]
    assert "property 123" in data["message"]
    # Should not contain preference information
    assert "Bedroom preference" not in data["message"]
    assert "Preferred move-in date" not in data["message"]


def test_schedule_tour_invalid_property_id_zero():
    """Test scheduling a tour with invalid property ID (zero)."""
    future_date = datetime.now() + timedelta(days=1)

    response = client.post(
        "/schedule",
        json={
            "property_id": 0,
            "renter_id": 456,
            "tour_date": future_date.isoformat(),
            "first_name": "John",
            "last_name": "Doe",
        },
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Property ID '0' is invalid" in response.text


def test_schedule_tour_invalid_property_id_negative():
    """Test scheduling a tour with invalid property ID (negative)."""
    future_date = datetime.now() + timedelta(days=1)

    response = client.post(
        "/schedule",
        json={
            "property_id": -1,
            "renter_id": 456,
            "tour_date": future_date.isoformat(),
            "first_name": "John",
            "last_name": "Doe",
        },
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Property ID '-1' is invalid" in response.text


def test_schedule_tour_invalid_renter_id_zero():
    """Test scheduling a tour with invalid renter ID (zero)."""
    future_date = datetime.now() + timedelta(days=1)

    response = client.post(
        "/schedule",
        json={
            "property_id": 123,
            "renter_id": 0,
            "tour_date": future_date.isoformat(),
            "first_name": "John",
            "last_name": "Doe",
        },
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Renter ID '0' is invalid" in response.text


def test_schedule_tour_invalid_renter_id_negative():
    """Test scheduling a tour with invalid renter ID (negative)."""
    future_date = datetime.now() + timedelta(days=1)

    response = client.post(
        "/schedule",
        json={
            "property_id": 123,
            "renter_id": -1,
            "tour_date": future_date.isoformat(),
            "first_name": "John",
            "last_name": "Doe",
        },
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Renter ID '-1' is invalid" in response.text


def test_schedule_tour_past_date():
    """Test scheduling a tour with a past date."""
    past_date = datetime.now() - timedelta(days=1)

    response = client.post(
        "/schedule",
        json={
            "property_id": 123,
            "renter_id": 456,
            "tour_date": past_date.isoformat(),
            "first_name": "John",
            "last_name": "Doe",
        },
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Tour date" in response.text
    assert "is invalid" in response.text


def test_schedule_tour_missing_property_id():
    """Test scheduling a tour without property ID."""
    future_date = datetime.now() + timedelta(days=1)

    response = client.post(
        "/schedule",
        json={"renter_id": 456, "tour_date": future_date.isoformat(), "first_name": "John", "last_name": "Doe"},
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Field required" in response.text


def test_schedule_tour_missing_renter_id():
    """Test scheduling a tour without renter ID."""
    future_date = datetime.now() + timedelta(days=1)

    response = client.post(
        "/schedule",
        json={"property_id": 123, "tour_date": future_date.isoformat(), "first_name": "John", "last_name": "Doe"},
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Field required" in response.text


def test_schedule_tour_missing_tour_date():
    """Test scheduling a tour without tour date."""
    response = client.post(
        "/schedule", json={"property_id": 123, "renter_id": 456, "first_name": "John", "last_name": "Doe"}
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Field required" in response.text


def test_schedule_tour_missing_first_name():
    """Test scheduling a tour without first name."""
    future_date = datetime.now() + timedelta(days=1)

    response = client.post(
        "/schedule",
        json={"property_id": 123, "renter_id": 456, "tour_date": future_date.isoformat(), "last_name": "Doe"},
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Field required" in response.text


def test_schedule_tour_missing_last_name():
    """Test scheduling a tour without last name."""
    future_date = datetime.now() + timedelta(days=1)

    response = client.post(
        "/schedule",
        json={"property_id": 123, "renter_id": 456, "tour_date": future_date.isoformat(), "first_name": "John"},
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "Field required" in response.text


def test_schedule_tour_invalid_bedrooms_preference():
    """Test scheduling a tour with invalid bedroom preference."""
    future_date = datetime.now() + timedelta(days=1)

    response = client.post(
        "/schedule",
        json={
            "property_id": 123,
            "renter_id": 456,
            "tour_date": future_date.isoformat(),
            "first_name": "John",
            "last_name": "Doe",
            "preference_bedrooms": 5,  # Should be 0-4 according to model
        },
    )

    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY


def test_schedule_tour_deterministic_appointment_id():
    """Test that the same input generates the same appointment ID."""
    future_date = datetime.now() + timedelta(days=1)

    request_data = {
        "property_id": 123,
        "renter_id": 456,
        "tour_date": future_date.isoformat(),
        "first_name": "John",
        "last_name": "Doe",
    }

    response1 = client.post("/schedule", json=request_data)
    response2 = client.post("/schedule", json=request_data)

    assert response1.status_code == status.HTTP_200_OK
    assert response2.status_code == status.HTTP_200_OK

    data1 = response1.json()
    data2 = response2.json()

    assert data1["appointment_id"] == data2["appointment_id"]
